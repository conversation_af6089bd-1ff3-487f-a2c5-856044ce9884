"""
HTML Comparison API endpoints for comparing HTML documents using LLM analysis.
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Dict, Any
import logging

from app.models.schemas import HTMLComparisonRequest, HTMLComparisonResponse, HTMLComparisonByIdRequest
from app.services.html_comparison_service import HTMLComparisonService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize HTML comparison service
html_comparison_service = HTMLComparisonService()


@router.post("/compare/by-id", response_model=HTMLComparisonResponse)
async def compare_html_documents_by_id(request: HTMLComparisonByIdRequest):
    """
    Compare two HTML documents by their report ID.
    """
    try:
        logger.info(f"Starting HTML comparison for report ID: {request.report_id}")
        result = await html_comparison_service.compare_html_by_id(request)
        logger.info(f"HTML comparison for report ID {request.report_id} completed. Comparison ID: {result.comparison_id}")
        return result
    except HTTPException as e:
        # Re-raise other HTTPException to preserve status code and detail
        raise e
    except Exception as e:
        logger.error(f"Error during HTML comparison for report ID {request.report_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred: {str(e)}"
        )

@router.post("/compare", response_model=HTMLComparisonResponse)
async def compare_html_documents(request: HTMLComparisonRequest):
    """
    Compare two HTML documents and identify differences using LLM analysis.
    
    This endpoint takes two HTML documents (requested order HTML and finalized order HTML)
    and uses AI to analyze the differences between them, providing detailed information
    about what was added, removed, or modified.
    
    Args:
        request: HTMLComparisonRequest containing the two HTML documents to compare
        
    Returns:
        HTMLComparisonResponse with detailed comparison results including:
        - Summary of changes
        - List of added elements
        - List of removed elements  
        - List of modified elements
        - Confidence score and processing time
        
    Raises:
        HTTPException: If the comparison fails or invalid input is provided
    """
    try:
        logger.info("Starting HTML comparison")
        
        # Validate input
        if not request.requested_order_html or not request.requested_order_html.strip():
            raise HTTPException(
                status_code=400, 
                detail="requested_order_html cannot be empty"
            )
        
        if not request.finalized_order_html or not request.finalized_order_html.strip():
            raise HTTPException(
                status_code=400, 
                detail="finalized_order_html cannot be empty"
            )
        
        # Perform comparison
        result = await html_comparison_service.compare_html(request)
        
        logger.info(f"HTML comparison completed. Comparison ID: {result.comparison_id}")
        logger.info(f"Total changes found: {result.total_changes}")
        
        return result
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error during HTML comparison: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during HTML comparison: {str(e)}"
        )


@router.post("/compare/summary", response_model=Dict[str, Any])
async def compare_html_summary(request: HTMLComparisonRequest):
    """
    Get a quick summary of differences between two HTML documents.
    
    This endpoint provides a lightweight comparison that returns only
    high-level statistics about the differences without detailed element analysis.
    
    Args:
        request: HTMLComparisonRequest containing the two HTML documents to compare
        
    Returns:
        Dictionary with summary information:
        - comparison_id: Unique identifier for the comparison
        - summary: Brief description of changes
        - total_changes: Number of changes detected
        - processing_time: Time taken for analysis
        - confidence_score: AI confidence in the analysis
        
    Raises:
        HTTPException: If the comparison fails or invalid input is provided
    """
    try:
        logger.info("Starting HTML comparison summary")
        
        # Validate input
        if not request.requested_order_html or not request.requested_order_html.strip():
            raise HTTPException(
                status_code=400, 
                detail="requested_order_html cannot be empty"
            )
        
        if not request.finalized_order_html or not request.finalized_order_html.strip():
            raise HTTPException(
                status_code=400, 
                detail="finalized_order_html cannot be empty"
            )
        
        # Perform comparison
        result = await html_comparison_service.compare_html(request)
        
        # Return summary only
        summary_result = {
            "comparison_id": result.comparison_id,
            "timestamp": result.timestamp,
            "summary": result.summary,
            "total_changes": result.total_changes,
            "changes_breakdown": {
                "added": len(result.added_elements),
                "removed": len(result.removed_elements),
                "modified": len(result.modified_elements)
            },
            "processing_time": result.processing_time,
            "confidence_score": result.confidence_score
        }
        
        logger.info(f"HTML comparison summary completed. Comparison ID: {result.comparison_id}")
        
        return summary_result
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error during HTML comparison summary: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during HTML comparison summary: {str(e)}"
        )


@router.get("/health")
async def health_check():
    """
    Health check endpoint for the HTML comparison service.
    
    Returns:
        Dictionary with service status and configuration information
    """
    try:
        # Check if LLM service is available
        llm_available = html_comparison_service.llm is not None
        
        return {
            "status": "healthy",
            "service": "HTML Comparison API",
            "llm_available": llm_available,
            "features": [
                "HTML document comparison",
                "AI-powered difference detection",
                "Structural and content analysis",
                "Detailed change reporting"
            ],
            "endpoints": [
                "/compare - Full HTML comparison with detailed results",
                "/compare/summary - Quick comparison summary",
                "/health - Service health check"
            ]
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Service health check failed: {str(e)}"
        )
