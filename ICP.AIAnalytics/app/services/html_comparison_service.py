"""
HTML Comparison Service using LLM for intelligent HTML comparison and analysis.
"""

from langchain_openai import ChatOpenAI
from langchain.schema import SystemMessage, HumanMessage
from typing import List, Dict, Any, Optional
import uuid
import time
from datetime import datetime
import json
import re
from bs4 import BeautifulSoup

import httpx
import logging
from app.core.config import settings
from app.models.schemas import HTMLComparisonRequest, HTMLComparisonResponse, HTMLDifference, HTMLComparisonByIdRequest
from fastapi import HTTPException
from app.services.rag_service import RAGService

class HTMLComparisonService:
    """Service for comparing HTML content using LLM analysis."""
    
    def __init__(self):
        """Initialize the HTML comparison service."""
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            openai_api_key=settings.OPENAI_API_KEY,
            temperature=0,
            seed=42,  # Deterministic results
            max_retries=3,
            max_tokens=settings.MAX_TOKENS
        ) if settings.OPENAI_API_KEY else None
        self.rag_service = RAGService()
        self.html_examples_collection = "html_comparison_examples"
        self.logger = logging.getLogger(__name__)

    async def initialize_html_comparison_knowledge_base(self, examples: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Initialize RAG knowledge base with correct HTML comparison examples."""
        return await self.rag_service.initialize_knowledge_base(examples)

    async def _fetch_html_from_api(self, report_id: str, token: str) -> Dict[str, str]:
        """Fetch HTML content from the external API and return only the relevant HTML fields."""
        if not settings.HTML_COMPARISON_API_URL:
            self.logger.error("HTML_COMPARISON_API_URL is not configured.")
            raise HTTPException(status_code=500, detail="HTML_COMPARISON_API_URL is not configured.")

        api_url = settings.HTML_COMPARISON_API_URL.format(reportId=report_id)
        headers = {
            "Authorization": f"Bearer {token}",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"
        }

        self.logger.info(f"Fetching HTML from API: {api_url}")
        self.logger.info(f"Using token (first 10 chars): {token[:10]}... Report ID: {report_id}")
        self.logger.info(f"Request headers: {headers}")

        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(api_url, headers=headers)
                self.logger.info(f"External API response status: {response.status_code}")
                self.logger.debug(f"External API response content: {response.text}")
                response.raise_for_status()
                data = response.json()
                # Ensure the expected keys are present
                if not ("requestedOrderHtml" in data and "finalizedOrderHTML" in data):
                    self.logger.error(f"API response missing expected keys: {data}")
                    raise HTTPException(status_code=502, detail="External API did not return expected HTML fields.")
                return {
                    "requestedOrderHtml": data["requestedOrderHtml"],
                    "finalizedOrderHTML": data["finalizedOrderHTML"]
                }
            except httpx.HTTPStatusError as e:
                self.logger.error(f"External API error: {e.response.status_code}")
                self.logger.error(f"Response body: {e.response.text}")
                raise HTTPException(
                    status_code=e.response.status_code, 
                    detail=f"External API error: {e.response.status_code}. Response: {e.response.text}"
                )
            except httpx.RequestError as e:
                self.logger.error(f"External API request failed: {e!r}")
                raise HTTPException(status_code=500, detail=f"External API request failed: {e!r}")
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to decode JSON response from external API: {e}")
                self.logger.error(f"Response text: {response.text}")
                raise HTTPException(status_code=500, detail="Failed to decode JSON response from external API.")

    async def compare_html_by_id(self, request: HTMLComparisonByIdRequest) -> HTMLComparisonResponse:
        """
        Compare two HTML documents fetched by report ID.
        """
        html_data = await self._fetch_html_from_api(request.report_id, request.bearer_token)
        
        comparison_request = HTMLComparisonRequest(
            requested_order_html=html_data.get("requestedOrderHtml", ""),
            finalized_order_html=html_data.get("finalizedOrderHTML", "")
        )
        
        return await self.compare_html(comparison_request)
        
    async def compare_html(self, request: HTMLComparisonRequest) -> HTMLComparisonResponse:
        """
        Compare two HTML documents and identify differences using LLM analysis.
        
        Args:
            request: HTMLComparisonRequest containing the two HTML documents
            
        Returns:
            HTMLComparisonResponse with detailed comparison results
        """
        start_time = time.time()
        comparison_id = str(uuid.uuid4())
        
        try:
            # Preprocess HTML content for better comparison
            cleaned_requested = self._clean_html(request.requested_order_html)
            cleaned_finalized = self._clean_html(request.finalized_order_html)
            
            # Generate LLM analysis
            if self.llm:
                analysis_result = await self._generate_llm_comparison(
                    cleaned_requested, 
                    cleaned_finalized
                )
            else:
                # Fallback to basic comparison if LLM is not available
                analysis_result = self._generate_fallback_comparison(
                    cleaned_requested, 
                    cleaned_finalized
                )
            
            # Parse the analysis result
            parsed_result = self._parse_comparison_result(analysis_result)

            # Calculate actual total changes from parsed elements
            actual_total_changes = (
                len(parsed_result.get("added_elements", [])) +
                len(parsed_result.get("removed_elements", [])) +
                len(parsed_result.get("modified_elements", []))
            )

            # Calculate processing time
            processing_time = time.time() - start_time

            # Build response
            response = HTMLComparisonResponse(
                comparison_id=comparison_id,
                timestamp=datetime.now(),
                summary=parsed_result.get("summary", "HTML comparison completed"),
                total_changes=actual_total_changes,  # Use calculated count
                added_elements=parsed_result.get("added_elements", []),
                removed_elements=parsed_result.get("removed_elements", []),
                modified_elements=parsed_result.get("modified_elements", []),
                processing_time=processing_time,
                confidence_score=parsed_result.get("confidence_score", 0.8)
            )
            
            return response
            
        except Exception as e:
            # Return error response
            return HTMLComparisonResponse(
                comparison_id=comparison_id,
                timestamp=datetime.now(),
                summary=f"Error during HTML comparison: {str(e)}",
                total_changes=0,
                added_elements=[],
                removed_elements=[],
                modified_elements=[],
                processing_time=time.time() - start_time,
                confidence_score=0.0
            )
    
    def _clean_html(self, html_content: str) -> str:
        """
        Clean and normalize HTML content for better comparison.
        
        Args:
            html_content: Raw HTML content
            
        Returns:
            Cleaned HTML content
        """
        try:
            # Parse HTML with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove comments
            for comment in soup.find_all(string=lambda text: isinstance(text, str) and text.strip().startswith('<!--')):
                comment.extract()
            
            # Normalize whitespace
            for element in soup.find_all(text=True):
                if element.strip():
                    element.replace_with(re.sub(r'\s+', ' ', element.strip()))
            
            # Return prettified HTML
            return soup.prettify()
            
        except Exception:
            # If parsing fails, return original content
            return html_content
    
    async def _generate_llm_comparison(self, html1: str, html2: str) -> str:
        # Retrieve relevant examples
        search_query = self._create_html_search_query(html1, html2)
        rag_data = await self.rag_service.retrieve_relevant_examples(
            search_query, 
            f"{html1[:500]}...{html2[:500]}", 
            n_results=3
        )
        
        # Build RAG-enhanced prompt
        system_prompt = self._build_rag_enhanced_html_prompt(rag_data)
        
        user_prompt = f"""Compare these HTML documents using the knowledge base examples:
        
        REQUESTED ORDER HTML: {html1}
        FINALIZED ORDER HTML: {html2}
        
        Follow the patterns shown in the examples for accurate change detection and counting."""
        
        response = await self.llm.ainvoke([
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ])
        return response.content
    
    def _generate_fallback_comparison(self, html1: str, html2: str) -> str:
        """
        Generate basic comparison when LLM is not available.
        
        Args:
            html1: First HTML document
            html2: Second HTML document
            
        Returns:
            Basic comparison result as JSON string
        """
        # Simple text-based comparison
        lines1 = html1.split('\n')
        lines2 = html2.split('\n')
        
        added_lines = []
        removed_lines = []
        
        # Basic line-by-line comparison
        for i, line in enumerate(lines2):
            if line not in lines1:
                added_lines.append(f"Line {i+1}: {line.strip()}")
        
        for i, line in enumerate(lines1):
            if line not in lines2:
                removed_lines.append(f"Line {i+1}: {line.strip()}")
        
        result = {
            "summary": f"Basic comparison found {len(added_lines)} additions and {len(removed_lines)} removals",
            "total_changes": len(added_lines) + len(removed_lines),
            "confidence_score": 0.5,
            "added_elements": [
                {
                    "type": "added",
                    "element": line,
                    "location": "unknown",
                    "description": f"Added content: {line[:100]}..."
                } for line in added_lines[:10]  # Limit to first 10
            ],
            "removed_elements": [
                {
                    "type": "removed",
                    "element": line,
                    "location": "unknown", 
                    "description": f"Removed content: {line[:100]}..."
                } for line in removed_lines[:10]  # Limit to first 10
            ],
            "modified_elements": []
        }
        
        return json.dumps(result)
    
    def _parse_comparison_result(self, analysis_result: str) -> Dict[str, Any]:
        """
        Parse the LLM analysis result into structured data.
        
        Args:
            analysis_result: Raw LLM response
            
        Returns:
            Parsed comparison data
        """
        try:
            # Try to parse as JSON
            parsed = json.loads(analysis_result)
            
            # Convert dictionaries to HTMLDifference objects
            added_elements = [
                HTMLDifference(**item) for item in parsed.get("added_elements", [])
            ]
            removed_elements = [
                HTMLDifference(**item) for item in parsed.get("removed_elements", [])
            ]
            modified_elements = [
                HTMLDifference(**item) for item in parsed.get("modified_elements", [])
            ]
            
            return {
                "summary": parsed.get("summary", "Comparison completed"),
                "total_changes": parsed.get("total_changes", 0),
                "confidence_score": parsed.get("confidence_score", 0.8),
                "added_elements": added_elements,
                "removed_elements": removed_elements,
                "modified_elements": modified_elements
            }
            
        except json.JSONDecodeError:
            # If JSON parsing fails, create a basic response
            return {
                "summary": "HTML comparison completed with parsing issues",
                "total_changes": 1,
                "confidence_score": 0.3,
                "added_elements": [],
                "removed_elements": [],
                "modified_elements": [
                    HTMLDifference(
                        type="modified",
                        element="document",
                        description="Unable to parse detailed changes",
                        location="unknown"
                    )
                ]
            }

    def _create_html_search_query(self, html1: str, html2: str) -> str:
        """Create search query for HTML comparison RAG retrieval."""
        # Extract key elements from both HTML documents
        key_elements = []
        
        # Simple extraction of common HTML elements and attributes
        for html in [html1, html2]:
            if 'table' in html.lower():
                key_elements.append('table comparison')
            if 'td' in html.lower():
                key_elements.append('cell changes')
            if 'class=' in html.lower():
                key_elements.append('style modifications')
            if 'id=' in html.lower():
                key_elements.append('element identification')
        
        # Create search query
        base_query = "HTML document comparison changes differences"
        if key_elements:
            base_query += " " + " ".join(set(key_elements))
        
        return base_query

    def _build_rag_enhanced_html_prompt(self, rag_data: Dict[str, Any]) -> str:
        """Build RAG-enhanced system prompt for HTML comparison."""
        base_prompt = """You are an expert HTML comparison analyst. Compare HTML documents and identify all differences with precise counting.

RESPONSE FORMAT: Return valid JSON with this exact structure:
{
    "summary": "Brief description of changes found",
    "total_changes": <exact_count_of_all_changes>,
    "confidence_score": <0.0_to_1.0>,
    "added_elements": [{"type": "added", "element": "name", "location": "path", "description": "what was added"}],
    "removed_elements": [{"type": "removed", "element": "name", "location": "path", "description": "what was removed"}],
    "modified_elements": [{"type": "modified", "element": "name", "location": "path", "old_value": "old", "new_value": "new", "description": "what changed"}]
}

COUNTING RULES:
- total_changes = len(added_elements) + len(removed_elements) + len(modified_elements)
- Count each distinct change only once
- Be precise and accurate"""

        # Add RAG examples if available
        if rag_data and rag_data.get('examples'):
            base_prompt += "\n\nKNOWLEDGE BASE EXAMPLES:"
            for i, example in enumerate(rag_data['examples'][:2], 1):
                base_prompt += f"\n\nExample {i}: {example.get('document', '')[:300]}..."

        return base_prompt
